@import "tailwindcss";

@layer base {
  :root {
    /* Scholarly Serenity Light Mode Only */
    --background: 210 40% 98%; /* Soft White #F8FAFC */
    --foreground: 215 25% 27%; /* Charcoal Gray #1F2937 */
    --card: 0 0% 100%; /* Pure White */
    --card-foreground: 215 25% 27%; /* Charcoal Gray #1F2937 */
    --popover: 0 0% 100%; /* Pure White */
    --popover-foreground: 215 25% 27%; /* Charcoal Gray #1F2937 */
    --primary: 221 83% 33%; /* Navy Blue #1E3A8A */
    --primary-foreground: 0 0% 100%; /* White */
    --secondary: 173 80% 36%; /* Teal #14B8A6 */
    --secondary-foreground: 0 0% 100%; /* White */
    --muted: 210 40% 96%; /* Light Gray */
    --muted-foreground: 215 16% 47%; /* Slate Gray #6B7280 */
    --accent: 173 80% 36%; /* Teal #14B8A6 */
    --accent-foreground: 0 0% 100%; /* White */
    --destructive: 0 84% 60%; /* Soft Red #F87171 */
    --destructive-foreground: 0 0% 100%; /* White */
    --border: 214 32% 91%; /* Light Border */
    --input: 214 32% 91%; /* Light Border */
    --ring: 221 83% 33%; /* Navy Blue #1E3A8A */
    --radius: 0.5rem;
    
    /* Success and Warning Colors */
    --success: 88 77% 78%; /* Lime Green #84CC16 */
    --success-foreground: 215 25% 27%; /* Charcoal Gray #1F2937 */
    --warning: 38 92% 50%; /* Amber #F59E0B */
    --warning-foreground: 215 25% 27%; /* Charcoal Gray #1F2937 */
    
    /* Sidebar Colors */
    --sidebar: 221 83% 33%; /* Navy Blue #1E3A8A */
    --sidebar-foreground: 0 0% 100%; /* White */
    --sidebar-primary: 173 80% 36%; /* Teal #14B8A6 */
    --sidebar-primary-foreground: 0 0% 100%; /* White */
    --sidebar-accent: 221 83% 40%; /* Lighter Navy */
    --sidebar-accent-foreground: 0 0% 100%; /* White */
    --sidebar-border: 221 83% 40%; /* Lighter Navy */
    --sidebar-ring: 173 80% 36%; /* Teal #14B8A6 */
    --sidebar-width: 16rem;
    --sidebar-width-icon: 8rem;
  }

  * {
    border-color: hsl(var(--border));
  }

  body {
    color: hsl(var(--foreground));
    background: hsl(var(--background));
    font-family: var(--font-montserrat), system-ui, sans-serif;
  }
}

/* Animated background for auth pages - Updated with Scholarly Serenity colors */
.auth-bg-animated {
  background: linear-gradient(-45deg, 
    #1E3A8A, /* Navy Blue */
    #14B8A6, /* Teal */
    #84CC16, /* Lime Green */
    #1E40AF, /* Lighter Navy */
    #2DD4BF, /* Brighter Teal */
    #A3E635  /* Brighter Lime */
  );
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Scholarly Serenity Theme Utilities */
.scholarly-gradient {
  background: linear-gradient(135deg, #1E3A8A 0%, #14B8A6 100%);
}

.scholarly-gradient-dark {
  background: linear-gradient(135deg, #1E40AF 0%, #2DD4BF 100%);
}

.scholarly-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: var(--radius);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.scholarly-button-primary {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border-radius: var(--radius);
  transition: all 0.2s ease-in-out;
}

.scholarly-button-primary:hover {
  background: hsl(var(--primary) / 0.9);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
}

.scholarly-button-secondary {
  background: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
  border-radius: var(--radius);
  transition: all 0.2s ease-in-out;
}

.scholarly-button-secondary:hover {
  background: hsl(var(--secondary) / 0.9);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(20, 184, 166, 0.3);
}

/* Sidebar Active State Enhancements */
.sidebar-active-item {
  position: relative;
  overflow: hidden;
}

.sidebar-active-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
  z-index: 1;
}

.sidebar-active-item > * {
  position: relative;
  z-index: 2;
}

/* Active tab indicator animation */
@keyframes activeTabGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
  }
}

.sidebar-active-glow {
  animation: activeTabGlow 2s ease-in-out infinite;
}



