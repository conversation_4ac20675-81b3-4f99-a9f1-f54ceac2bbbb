"use client";

import { ReactNode } from "react";
import { DashboardSidebar } from "@/components/sidebar";
import { Header } from "@/components/header";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";

interface DashboardLayoutProps {
  children: ReactNode;
  className?: string;
}

export function DashboardLayout({ children, className }: DashboardLayoutProps) {
  return (
    <SidebarProvider defaultOpen={true}>
      <div className="flex h-screen w-full">
        {/* Sidebar */}
        <DashboardSidebar />

        {/* Main Content Area */}
        <SidebarInset className="flex-1 w-full min-w-0">
          <div className="flex flex-col h-full w-full">
            {/* Header */}
            <Header />

            {/* Main Content */}
            <main className={`flex-1 overflow-y-auto p-6 w-full ${className}`}>
              {children}
            </main>
          </div>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}
