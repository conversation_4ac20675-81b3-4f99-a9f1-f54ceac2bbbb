"use client";

import { useAuth } from "@/components/auth-provider";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Bell,
  Sparkles,
  Flame,
  Settings,
  Calendar,
  Target,
  User,
  Award,
  LogOut,
} from "lucide-react";
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface HeaderProps {
  className?: string;
}

export function Header({ className }: HeaderProps) {
  const { user, signOut } = useAuth();

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 17) return "Good afternoon";
    return "Good evening";
  };

  const getMotivationalMessage = () => {
    const messages = [
      "Ready to ace your CSS exam today?",
      "Every study session brings you closer to success!",
      "Your dedication will open doors to opportunity.",
      "Stay focused, stay determined!",
      "Today's effort is tomorrow's achievement.",
    ];
    return messages[Math.floor(Math.random() * messages.length)];
  };

  const getCurrentDate = () => {
    const now = new Date();
    const options: Intl.DateTimeFormatOptions = {
      weekday: "long",
      month: "short",
      day: "numeric",
    };
    return now.toLocaleDateString("en-US", options);
  };

  const getCurrentTime = () => {
    const now = new Date();
    return now.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <header
      className={`border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-6 py-4 w-full ${className}`}
    >
      <div className="flex items-center justify-between w-full">
        {/* Left side - Greeting */}
        <div className="flex items-center space-x-6">
          <SidebarTrigger className="lg:hidden" />

          <div className="min-w-[280px]">
            <h1 className="text-xl font-semibold text-foreground">
              {getGreeting()},{" "}
              {user?.user_metadata?.name?.split(" ")[0] || "Student"}!
            </h1>
            <p className="text-sm text-muted-foreground flex items-center gap-2">
              <Sparkles className="h-3 w-3 text-yellow-500 flex-shrink-0" />
              <span className="truncate">{getMotivationalMessage()}</span>
            </p>
          </div>
        </div>

        {/* Center - Date & Time */}
        <div className="hidden md:flex items-center space-x-3 text-sm text-muted-foreground">
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4" />
            <span>{getCurrentDate()}</span>
          </div>
          <div className="text-sm font-medium">
            {getCurrentTime()}
          </div>
        </div>

        {/* Right side - Controls */}
        <div className="flex items-center space-x-3">
          {/* Study Streak Badge */}
          <div className="hidden sm:block">
            <Badge
              variant="secondary"
              className="bg-orange-100 text-orange-800 border-orange-300 hover:bg-orange-200 cursor-pointer"
            >
              <Flame className="h-3 w-3 mr-1" />7
            </Badge>
          </div>

          {/* Notifications */}
          <Button
            variant="ghost"
            size="sm"
            className="relative h-8 w-8 p-0 hover:bg-accent/50 transition-all duration-300 border border-border rounded-full cursor-pointer"
          >
            <Bell className="h-4 w-4" />
            <span className="absolute -top-2 -right-2 h-4 w-4 bg-destructive rounded-full text-xs text-destructive-foreground flex items-center justify-center font-bold">
              3
            </span>
          </Button>

          {/* Quick Settings */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-9 w-9 p-0 hover:bg-accent/50 transition-all duration-300 cursor-pointer"
              >
                <Settings className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-56 border border-gray-200"
              style={
                {
                  backgroundColor: "white",
                  color: "#111827",
                } as React.CSSProperties
              }
              align="end"
              forceMount
            >
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    Quick Settings
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    Study streak: 7 days
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="cursor-pointer hover:bg-gray-100 transition-colors duration-200">
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="cursor-pointer hover:bg-gray-100 transition-colors duration-200">
                <Target className="mr-2 h-4 w-4" />
                <span>Goals</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="cursor-pointer hover:bg-gray-100 transition-colors duration-200">
                <Award className="mr-2 h-4 w-4" />
                <span>Achievements</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleSignOut} className="cursor-pointer hover:bg-red-50 hover:text-red-700 transition-colors duration-200">
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
