"use client"

const SIDEBAR_COOKIE_NAME = "sidebar_state"
const SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7 // 7 days

export interface CookieOptions {
  path?: string
  maxAge?: number
  secure?: boolean
  httpOnly?: boolean
  sameSite?: "strict" | "lax" | "none"
}

export function setCookie(
  name: string,
  value: string,
  options: CookieOptions = {}
): void {
  if (typeof window === "undefined") {
    // SSR - cookies should be set on the server
    return
  }

  const {
    path = "/",
    maxAge = SIDEBAR_COOKIE_MAX_AGE,
    secure = process.env.NODE_ENV === "production",
    sameSite = "lax"
  } = options

  let cookieString = `${name}=${encodeURIComponent(value)}; path=${path}; max-age=${maxAge}; samesite=${sameSite}`
  
  if (secure) {
    cookieString += "; secure"
  }

  document.cookie = cookieString
}

export function getCookie(name: string): string | null {
  if (typeof window === "undefined") {
    // SSR - return null, cookies should be read on the server
    return null
  }

  const value = `; ${document.cookie}`
  const parts = value.split(`; ${name}=`)
  if (parts.length === 2) {
    return decodeURIComponent(parts.pop()?.split(";").shift() || "")
  }
  return null
}

export function deleteCookie(name: string, path: string = "/"): void {
  if (typeof window === "undefined") {
    return
  }

  document.cookie = `${name}=; path=${path}; expires=Thu, 01 Jan 1970 00:00:00 GMT`
}

// Sidebar-specific cookie functions
export function setSidebarState(state: boolean): void {
  setCookie(SIDEBAR_COOKIE_NAME, state.toString(), {
    path: "/",
    maxAge: SIDEBAR_COOKIE_MAX_AGE,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax"
  })
}

export function getSidebarState(): boolean | null {
  const value = getCookie(SIDEBAR_COOKIE_NAME)
  return value === null ? null : value === "true"
}
