import { cookies } from "next/headers"

const SIDEBAR_COOKIE_NAME = "sidebar_state"
const SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7 // 7 days

export interface ServerCookieOptions {
  path?: string
  maxAge?: number
  secure?: boolean
  httpOnly?: boolean
  sameSite?: "strict" | "lax" | "none"
}

// Server-side cookie functions
export async function getServerCookie(name: string): Promise<string | null> {
  try {
    const cookieStore = await cookies()
    return cookieStore.get(name)?.value || null
  } catch {
    return null
  }
}

export async function setServerCookie(
  name: string,
  value: string,
  options: ServerCookieOptions = {}
): Promise<void> {
  try {
    const cookieStore = await cookies()
    const {
      path = "/",
      maxAge = SIDEBAR_COOKIE_MAX_AGE,
      secure = process.env.NODE_ENV === "production",
      sameSite = "lax"
    } = options

    cookieStore.set(name, value, {
      path,
      maxAge,
      secure,
      sameSite: sameSite as "strict" | "lax" | "none"
    })
  } catch {
    // Silently fail if cookies are not available
  }
}

export async function deleteServerCookie(name: string): Promise<void> {
  try {
    const cookieStore = await cookies()
    cookieStore.delete(name)
  } catch {
    // Silently fail if cookies are not available
  }
}

// Sidebar-specific server cookie functions
export async function getServerSidebarState(): Promise<boolean | null> {
  const value = await getServerCookie(SIDEBAR_COOKIE_NAME)
  return value === null ? null : value === "true"
}

export async function setServerSidebarState(state: boolean): Promise<void> {
  await setServerCookie(SIDEBAR_COOKIE_NAME, state.toString(), {
    path: "/",
    maxAge: SIDEBAR_COOKIE_MAX_AGE,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax"
  })
}
