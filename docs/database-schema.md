# CSS Dost - Database Schema

This document outlines the comprehensive database schema for the CSS Dost platform using Prisma, supporting advanced AI-powered features and comprehensive user engagement.

## Overview

The schema is designed to be modular and scalable, covering user management, content organization, mock tests, essay writing, AI analysis, peer review, gamification, and community features.

### `schema.prisma`

```prisma
// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_PRISMA_URL")
}

// User Management
model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  role          UserRole  @default(USER)
  accounts      Account[]
  sessions      Session[]
  profile       Profile?
  testResults   TestResult[]
  essays        Essay[]
  reviews       Review[]
  posts         Post[]
  comments      Comment[]
  badges        UserBadge[]
  challenges    DailyChallenge[]
  notifications Notification[]
  topicScores   TopicScore[]
  handwritingScores HandwritingScore[]
  activityLogs  ActivityLog[]
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
}

model Profile {
  id          String @id @default(cuid())
  bio         String?
  user        User   @relation(fields: [userId], references: [id])
  userId      String @unique
  studyStreak Int    @default(0)
  longestStreak Int  @default(0)
  lastActivityDate DateTime?
  preferences Json?  // User preferences for notifications, themes, etc.
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

enum UserRole {
  USER
  ADMIN
}

// NextAuth.js specific models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Content Structure
model Subject {
  id          String     @id @default(cuid())
  title       String     @unique
  description String?
  materials   Material[]
  videos      Video[]
  tests       Test[]
  essays      Essay[]
  mindMaps    MindMap[]
  topicScores TopicScore[]
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
}

model Material {
  id          String   @id @default(cuid())
  title       String
  contentUrl  String // URL to PDF or other material
  contentType String   @default("PDF")
  subject     Subject  @relation(fields: [subjectId], references: [id])
  subjectId   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Video {
  id          String  @id @default(cuid())
  title       String
  youtubeUrl  String
  subject     Subject @relation(fields: [subjectId], references: [id])
  subjectId   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model MindMap {
  id          String   @id @default(cuid())
  title       String
  visualizationUrl String
  coveragePercentage Float
  subject     Subject  @relation(fields: [subjectId], references: [id])
  subjectId   String
  data        Json     // Mind map structure and data
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// Mock Tests
model Test {
  id          String       @id @default(cuid())
  title       String
  subject     Subject      @relation(fields: [subjectId], references: [id])
  subjectId   String
  questions   Question[]
  testResults TestResult[]
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
}

model Question {
  id        String   @id @default(cuid())
  text      String
  test      Test     @relation(fields: [testId], references: [id])
  testId    String
  options   Option[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Option {
  id         String   @id @default(cuid())
  text       String
  isCorrect  Boolean  @default(false)
  question   Question @relation(fields: [questionId], references: [id])
  questionId String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model TestResult {
  id        String   @id @default(cuid())
  score     Float
  test      Test     @relation(fields: [testId], references: [id])
  testId    String
  user      User     @relation(fields: [userId], references: [id])
  userId    String
  answers   Json?    // Store user's answers for analysis
  timeSpent Int?     // Time spent in seconds
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Essay Writing & AI Analysis
model Essay {
  id          String   @id @default(cuid())
  title       String
  content     String
  subject     Subject  @relation(fields: [subjectId], references: [id])
  subjectId   String
  author      User     @relation(fields: [authorId], references: [id])
  authorId    String
  analysis    EssayAnalysis?
  reviews     Review[]
  isAnonymous Boolean  @default(false)
  status      EssayStatus @default(DRAFT)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([authorId])
  @@index([subjectId])
  @@index([status])
}

model EssayAnalysis {
  id              String  @id @default(cuid())
  essay           Essay   @relation(fields: [essayId], references: [id])
  essayId         String  @unique
  grammarScore    Float?
  vocabularyScore Float?
  relevanceScore  Float?
  overallScore    Float?
  heatmapData     Json?   // Color-coded sentence feedback
  gapAnalysis     Json?   // Missing syllabus areas
  suggestions     Json?   // AI suggestions for improvement
  processingStatus AnalysisStatus @default(PENDING)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

enum EssayStatus {
  DRAFT
  SUBMITTED
  UNDER_REVIEW
  REVIEWED
  PUBLISHED
  ARCHIVED
}

enum AnalysisStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

// Peer Review System
model Review {
  id           String   @id @default(cuid())
  essay        Essay    @relation(fields: [essayId], references: [id])
  essayId      String
  reviewer     User     @relation(fields: [reviewerId], references: [id])
  reviewerId   String
  rubricScores Json     // Structured rubric scores
  feedback     String
  quality      Float    // AI-assessed review quality
  isAnonymous  Boolean  @default(true)
  status       ReviewStatus @default(PENDING)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@index([essayId])
  @@index([reviewerId])
  @@index([status])
}

enum ReviewStatus {
  PENDING
  APPROVED
  REJECTED
  FLAGGED
}

// Daily Challenges
model DailyChallenge {
  id          String   @id @default(cuid())
  question    String
  answer      String
  subject     String
  timeLimit   Int      // Time limit in minutes
  user        User     @relation(fields: [userId], references: [id])
  userId      String
  userAnswer  String?
  score       Float?
  timeSpent   Int?     // Time spent in seconds
  completed   Boolean  @default(false)
  challengeDate DateTime
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([userId])
  @@index([challengeDate])
}

// Gamification & Achievements
model Badge {
  id          String   @id @default(cuid())
  name        String   @unique
  description String
  icon        String   // Icon URL or identifier
  category    BadgeCategory
  criteria    Json     // Badge unlocking criteria
  users       UserBadge[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model UserBadge {
  id        String   @id @default(cuid())
  user      User     @relation(fields: [userId], references: [id])
  userId    String
  badge     Badge    @relation(fields: [badgeId], references: [id])
  badgeId   String
  earnedAt  DateTime @default(now())
  claimedAt DateTime?

  @@unique([userId, badgeId])
  @@index([userId])
  @@index([badgeId])
}

enum BadgeCategory {
  GRAMMAR
  VOCABULARY
  ESSAY_COUNT
  STREAKS
  PEER_REVIEWS
  CHALLENGES
  SPECIAL
}

// Progress Tracking & Analytics
model TopicScore {
  id          String   @id @default(cuid())
  user        User     @relation(fields: [userId], references: [id])
  userId      String
  subject     Subject  @relation(fields: [subjectId], references: [id])
  subjectId   String
  topic       String
  score       Float
  confidence  Float    // Confidence level in the score
  lastUpdated DateTime @default(now())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([userId, subjectId, topic])
  @@index([userId])
  @@index([subjectId])
}

model HandwritingScore {
  id              String   @id @default(cuid())
  user            User     @relation(fields: [userId], references: [id])
  userId          String
  clarityScore    Float
  consistencyScore Float
  readabilityScore Float
  overallScore    Float
  improvementTips Json     // AI-generated improvement suggestions
  imageUrl        String?  // URL to uploaded handwriting image
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([userId])
}

// Activity Tracking
model ActivityLog {
  id        String       @id @default(cuid())
  user      User         @relation(fields: [userId], references: [id])
  userId    String
  activity  ActivityType
  details   Json?        // Additional activity details
  createdAt DateTime     @default(now())

  @@index([userId])
  @@index([activity])
  @@index([createdAt])
}

enum ActivityType {
  TEST_COMPLETED
  ESSAY_SUBMITTED
  REVIEW_SUBMITTED
  CHALLENGE_COMPLETED
  BADGE_EARNED
  STREAK_UPDATED
  LOGIN
  LOGOUT
}

// Notifications
model Notification {
  id        String           @id @default(cuid())
  user      User             @relation(fields: [userId], references: [id])
  userId    String
  type      NotificationType
  title     String
  message   String
  data      Json?            // Additional notification data
  isRead    Boolean          @default(false)
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  @@index([userId])
  @@index([isRead])
  @@index([createdAt])
}

enum NotificationType {
  WEAK_TOPIC_ALERT
  ACHIEVEMENT_UNLOCKED
  DAILY_CHALLENGE
  PEER_REVIEW_REQUEST
  SYSTEM_UPDATE
  GENERAL
}

// Community / Forum (Enhanced)
model Post {
  id              String           @id @default(cuid())
  title           String
  content         String
  author          User             @relation(fields: [authorId], references: [id])
  authorId        String
  comments        Comment[]
  moderationStatus ModerationStatus @default(PENDING)
  aiAnalysis      Json?            // AI content analysis results
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  @@index([authorId])
  @@index([moderationStatus])
}

model Comment {
  id              String           @id @default(cuid())
  content         String
  author          User             @relation(fields: [authorId], references: [id])
  authorId        String
  post            Post             @relation(fields: [postId], references: [id])
  postId          String
  moderationStatus ModerationStatus @default(PENDING)
  aiAnalysis      Json?            // AI content analysis results
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  @@index([authorId])
  @@index([postId])
  @@index([moderationStatus])
}

enum ModerationStatus {
  PENDING
  APPROVED
  REJECTED
  FLAGGED
}

// AI Processing Tasks
model AITask {
  id              String      @id @default(cuid())
  type            AITaskType
  status          AITaskStatus @default(PENDING)
  input           Json        // Input data for AI processing
  output          Json?       // AI processing results
  progress        Float       @default(0)
  error           String?     // Error message if failed
  estimatedCompletion DateTime?
  startedAt       DateTime?
  completedAt     DateTime?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  @@index([type])
  @@index([status])
  @@index([createdAt])
}

enum AITaskType {
  ESSAY_ANALYSIS
  OCR_PROCESSING
  PARAPHRASING
  GAP_ANALYSIS
  CONTENT_MODERATION
}

enum AITaskStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}

// System Configuration
model SystemConfig {
  id          String @id @default(cuid())
  key         String @unique
  value       Json
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
```

## Database Indexes & Performance

### Primary Indexes
- User email for authentication
- User-subject-topic combinations for topic scores
- Essay status and author for efficient querying
- Review status and relationships
- Activity logs by user and date
- Notifications by user and read status

### Composite Indexes
- User-badge combinations for achievement tracking
- Challenge completion by user and date
- Test results by user and test
- Reviews by essay and reviewer

### Performance Considerations
- Partition large tables (activity logs, notifications) by date
- Use materialized views for complex analytics queries
- Implement caching for frequently accessed data
- Optimize JSON field queries with GIN indexes

## Data Relationships

### User-Centric Relationships
- User → Profile (1:1)
- User → Essays (1:many)
- User → Reviews (1:many)
- User → Badges (many:many through UserBadge)
- User → TestResults (1:many)
- User → ActivityLogs (1:many)

### Content Relationships
- Subject → Materials (1:many)
- Subject → Videos (1:many)
- Subject → Tests (1:many)
- Subject → Essays (1:many)
- Subject → MindMaps (1:many)

### AI & Analysis Relationships
- Essay → EssayAnalysis (1:1)
- Essay → Reviews (1:many)
- User → HandwritingScores (1:many)
- User → TopicScores (1:many)

## Data Privacy & Retention

### PII Handling
- Minimal PII stored: name, email, optional image and bio
- User consent tracking for AI data usage
- Data anonymization for AI training where applicable

### Retention Policies
- Essays and reviews retained for learning purposes unless deleted by user
- Activity logs retained for 2 years for analytics
- Test results retained indefinitely for progress tracking
- AI processing data retained for 30 days unless user requests deletion

### GDPR Compliance
- Right to be forgotten implementation
- Data export functionality
- Consent management for AI features
- Data portability for user content

## Migration Strategy

### Phase 1: Core Tables
1. User, Profile, Subject, Material, Video
2. Test, Question, Option, TestResult
3. Basic authentication tables

### Phase 2: Essay & AI Features
1. Essay, EssayAnalysis
2. Review system
3. AI processing tables

### Phase 3: Gamification & Analytics
1. Badge system
2. Progress tracking
3. Activity logging
4. Notifications

### Phase 4: Advanced Features
1. Mind maps
2. Handwriting analysis
3. Daily challenges
4. Enhanced forum features

## Environment Variables

```env
# Database
POSTGRES_PRISMA_URL="postgresql://..."
DATABASE_URL="postgresql://..."

# AI Services
OPENAI_API_KEY="..."
GOOGLE_CLOUD_VISION_API_KEY="..."

# File Storage
CLOUDINARY_URL="..."
SUPABASE_STORAGE_URL="..."

# Notifications
PUSH_NOTIFICATION_KEY="..."
EMAIL_SERVICE_API_KEY="..."
```

Note:
- In Vercel, ensure all environment variables are properly configured
- Use connection pooling for production database connections
- Implement database backup and recovery procedures
- Monitor database performance and optimize queries as needed
``` 

Note:
- In Vercel, ensure `POSTGRES_PRISMA_URL` is set to your Supabase Prisma connection string.
- Optionally, also define `DATABASE_URL` with the same value for local tools that expect it.