# CSS Dost - UI Components

This document outlines the component hierarchy for the CSS Dost platform. We will use Tailwind CSS for styling and strive for a consistent, modern design.

## 🎨 Design System & Theming

- **Primary Color:** A professional blue (`#005A9C`)
- **Secondary Color:** A neutral gray (`#F0F2F5`)
- **Typography:** `Inter` font, sourced from Google Fonts via `next/font`.
- **Layout:** Consistent padding and spacing using Tailwind's spacing scale.
- **AI Feedback Colors:** Green (`#10B981`), Yellow (`#F59E0B`), Orange (`#F97316`), Red (`#EF4444`)

## 📁 Component Structure (`/components`)

### Layout Components (`/components/layout`)

- **`Navbar.tsx`**: Top navigation bar. Shows user profile/login button, links to subjects, forum, etc.
- **`Footer.tsx`**: Site footer with links and copyright info.
- **`Sidebar.tsx`**: (For admin/dashboard) Navigation sidebar.
- **`MainLayout.tsx`**: Wraps pages with Navbar and Footer.
- **`DashboardLayout.tsx`**: Enhanced dashboard layout with sidebar navigation.

### UI Primitives (`/components/ui`)

- **`Button.tsx`**: Standard button with variants (primary, secondary, danger).
- **`Card.tsx`**: A generic card component for wrapping content.
- **`Input.tsx`**: Styled input field.
- **`Modal.tsx`**: For pop-up dialogs.
- **`Spinner.tsx`**: Loading indicator.
- **`Tooltip.tsx`**: Enhanced tooltip for detailed feedback.
- **`Progress.tsx`**: Progress bars and circular progress indicators.
- **`Badge.tsx`**: Achievement badges and status indicators.
- **`Tabs.tsx`**: Tab navigation for different content sections.
- **`Alert.tsx`**: Alert messages for notifications and warnings.

### Feature Components (`/components/features`)

#### Content (`/components/features/content`)

- **`SubjectCard.tsx`**: Displays a single CSS subject on the main subjects page.
- **`MaterialCard.tsx`**: Displays a single study material (e.g., PDF link).
- **`VideoCard.tsx`**: Displays a single YouTube video thumbnail and title.
- **`MindMapViewer.tsx`**: Interactive mind map visualization for syllabus coverage.
- **`TopicGapAnalysis.tsx`**: Visual representation of missing syllabus areas.

#### Tests (`/components/features/tests`)

- **`TestRunner.tsx`**: The main component for the test-taking experience. Manages state like timer, current question index, and user answers.
- **`QuestionCard.tsx`**: Renders a single question with its options.
- **`TestResultSummary.tsx`**: Shows the final score and a brief summary after a test is completed.
- **`DailyChallenge.tsx`**: Daily past paper challenge interface with timer and scoring.

#### Essay Writing (`/components/features/essays`)

- **`EssayEditor.tsx`**: Rich text editor with real-time AI feedback integration.
- **`EssayHeatmap.tsx`**: Color-coded sentence feedback visualization.
- **`ParaphrasingCoach.tsx`**: Interface for AI-powered sentence transformation suggestions.
- **`HandwritingUpload.tsx`**: File upload interface for handwritten essays with OCR preview.
- **`EssayAnalysis.tsx`**: Comprehensive essay analysis results display.

#### User (`/components/features/user`)

- **`UserProfile.tsx`**: Displays user information and achievements.
- **`ProgressRadarChart.tsx`**: Multi-dimensional progress visualization using radar charts.
- **`AuthButtons.tsx`**: Client component showing Login/Register or Logout/Dashboard buttons based on session status.
- **`AchievementBadges.tsx`**: Display and management of user achievement badges.
- **`StudyStreak.tsx`**: Visual representation of study streaks and milestones.

#### Analytics (`/components/features/analytics`)

- **`ProgressDashboard.tsx`**: Comprehensive progress tracking dashboard.
- **`WeakTopicAlerts.tsx`**: Notification system for low-scoring areas.
- **`PerformanceCharts.tsx`**: Various chart types for performance visualization.
- **`TrendAnalysis.tsx`**: Trend analysis and prediction components.

#### Peer Review (`/components/features/reviews`)

- **`PeerReviewPanel.tsx`**: Interface for reviewing other users' essays.
- **`ReviewRubric.tsx`**: Guided rubric system for consistent feedback.
- **`ReviewQueue.tsx`**: Management of essays available for review.
- **`ReviewFeedback.tsx`**: Display of peer review feedback on user's essays.

#### Forum (`/components/features/forum`)

- **`PostCard.tsx`**: Displays a summary of a forum post.
- **`Comment.tsx`**: Displays a single comment.
- **`ForumModeration.tsx`**: AI-assisted content moderation interface.

#### Admin (`/components/features/admin`)

- **`AdminDashboard.tsx`**: The main interface for admin tasks.
- **`ContentTable.tsx`**: A reusable table to display and manage content (subjects, materials, etc.).
- **`ContentForm.tsx`**: A form to create/edit content.
- **`AnalyticsOverview.tsx`**: System-wide analytics and performance metrics.
- **`UserManagement.tsx`**: User management and role assignment interface.
- **`AIModelManagement.tsx`**: AI feature performance monitoring and optimization.

### Dashboard Components (`/components/dashboard`)

- **`DashboardOverview.tsx`**: Main dashboard overview with key metrics.
- **`QuickActions.tsx`**: Quick action buttons for common tasks.
- **`RecentActivity.tsx`**: Recent user activity feed.
- **`UpcomingChallenges.tsx`**: Display of upcoming daily challenges.
- **`PeerReviewQueue.tsx`**: Essays available for peer review.
- **`AchievementProgress.tsx`**: Progress toward next achievement badges.

### AI Integration Components (`/components/ai`)

- **`AIProcessingIndicator.tsx`**: Loading indicator for AI processing tasks.
- **`AIFeedbackDisplay.tsx`**: Display of AI-generated feedback and suggestions.
- **`AISuggestionCard.tsx`**: Individual AI suggestion with accept/reject options.
- **`AIErrorHandler.tsx`**: Graceful error handling for AI service failures.

### Notification Components (`/components/notifications`)

- **`NotificationCenter.tsx`**: Centralized notification management.
- **`WeakTopicAlert.tsx`**: Specific alert for weak topic notifications.
- **`AchievementNotification.tsx`**: Notification for unlocked achievements.
- **`DailyChallengeReminder.tsx`**: Reminder for daily challenges.

### Mobile-Specific Components (`/components/mobile`)

- **`MobileCamera.tsx`**: Camera interface for handwriting upload.
- **`MobileOfflineIndicator.tsx`**: Offline mode indicator and sync status.
- **`MobileNavigation.tsx`**: Mobile-optimized navigation interface.

## 🎯 Component Specifications

### Progress Radar Chart
- **Library:** Recharts or Chart.js
- **Dimensions:** English, Syllabus Coverage, Writing Style, Speed
- **Features:** Interactive tooltips, trend lines, peer comparison
- **Responsive:** Mobile-optimized touch interactions

### Essay Heatmap
- **Color Coding:** Green (excellent), Yellow (good), Orange (needs improvement), Red (poor)
- **Interactions:** Hover tooltips with detailed feedback
- **Accessibility:** High contrast mode support
- **Real-time:** Updates as user types or uploads

### Mind Map Viewer
- **Library:** D3.js or React Flow
- **Features:** Interactive nodes, zoom/pan, topic mastery indicators
- **Data:** Real-time updates based on user progress
- **Export:** PDF/image export capabilities

### Achievement Badges
- **Design:** Consistent badge design system
- **Categories:** Grammar, Vocabulary, Essay Count, Streaks, Peer Reviews
- **Progress:** Visual progress indicators toward next badge
- **Sharing:** Social media integration for badge sharing

### Peer Review Interface
- **Rubric System:** Guided scoring with predefined criteria
- **Anonymity:** Anonymous review system
- **Quality Control:** AI moderation indicators
- **Feedback:** Rich text feedback with formatting options

## 🔧 Technical Implementation Notes

### State Management
- **Global State:** Zustand or Redux for user data and preferences
- **Local State:** React hooks for component-specific state
- **Server State:** React Query for API data management

### Performance Optimization
- **Lazy Loading:** Code splitting for large components
- **Virtualization:** For long lists (essays, reviews, etc.)
- **Caching:** Intelligent caching for AI responses and user data
- **Debouncing:** For real-time AI feedback to prevent excessive API calls

### Accessibility
- **ARIA Labels:** Comprehensive accessibility support
- **Keyboard Navigation:** Full keyboard accessibility
- **Screen Reader:** Optimized for screen reader compatibility
- **Color Contrast:** WCAG AA compliance for all color combinations

### Responsive Design
- **Mobile First:** Mobile-optimized design approach
- **Breakpoints:** Consistent breakpoint system
- **Touch Interactions:** Optimized for touch devices
- **Offline Support:** Graceful degradation for offline scenarios

## 📱 Mobile Considerations

### Touch Interactions
- **Touch Targets:** Minimum 44px touch targets
- **Gestures:** Swipe gestures for navigation
- **Feedback:** Haptic feedback for important actions

### Performance
- **Bundle Size:** Optimized bundle sizes for mobile
- **Image Optimization:** Responsive images and lazy loading
- **Network:** Efficient API calls and caching strategies

### Offline Capabilities
- **Service Workers:** Offline functionality for core features
- **Data Sync:** Background sync when connection restored
- **Offline Indicators:** Clear indication of offline status 