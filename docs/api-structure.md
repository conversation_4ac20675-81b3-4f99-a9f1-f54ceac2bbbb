# CSS Dost - API Structure

This document outlines the API endpoints for the CSS Dost platform, following RESTful principles and supporting advanced AI-powered features.

## Base URL
`/api`

## Authentication
- Handled by <PERSON><PERSON><PERSON> (email/password and OAuth). Sessions managed via Supabase cookies and Next.js middleware.
- Required envs: `NEXT_PUBLIC_SUPABASE_URL`, `NEXT_PUBLIC_SUPABASE_ANON_KEY`, `NEXT_PUBLIC_SITE_URL`.
- The `/auth/callback` route exchanges the code for a session.

## 📚 Subjects API

- **`GET /api/subjects`**
  - **Description:** Fetches a list of all subjects.
  - **Response Body:** `Subject[]`

- **`GET /api/subjects/:subjectId`**
  - **Description:** Fetches a single subject with its materials, videos, and mind maps.
  - **Response Body:** `Subject` with `materials`, `videos`, and `mindMaps` included.

- **`GET /api/subjects/:subjectId/mind-map`**
  - **Description:** Fetches AI-generated mind map for syllabus coverage.
  - **Response Body:** `MindMap` with interactive visualization data.

## 📝 Materials API

- **`GET /api/materials/:materialId`**
  - **Description:** Fetches a single material.
  - **Response Body:** `Material`

## 🎬 Videos API

- **`GET /api/videos/youtube?channelId=[ID]`**
  - **Description:** Fetches videos from a specific YouTube channel using the YouTube API. This will be a server-side route handler.
  - **Response Body:** `any[]` (YouTube API video format)

## 🧪 Mock Tests API

- **`POST /api/tests/start`**
  - **Description:** Starts a new test session for a user.
  - **Request Body:** `{ testId: string }`
  - **Response Body:** `{ testSessionId: string, questions: Question[] }` (Questions won't include the `isCorrect` field)

- **`POST /api/tests/submit`**
  - **Description:** Submits a completed test.
  - **Request Body:** `{ testSessionId: string, answers: { questionId: string, optionId: string }[] }`
  - **Response Body:** `{ testResultId: string, score: number }`

- **`GET /api/tests/results/:resultId`**
  - **Description:** Fetches the result of a specific test.
  - **Response Body:** `TestResult` with details about correct/incorrect answers.

## ✍️ Essay Writing & AI Analysis API

- **`POST /api/essays`**
  - **Description:** Creates a new essay with AI analysis.
  - **Request Body:** `{ title: string, content: string, subjectId: string }`
  - **Response Body:** `{ essayId: string, analysisResults: EssayAnalysis }`

- **`GET /api/essays/:essayId`**
  - **Description:** Fetches a single essay with analysis results.
  - **Response Body:** `Essay` with `analysis` and `reviews` included.

- **`GET /api/essays/:essayId/heatmap`**
  - **Description:** Fetches color-coded feedback heatmap for essay sentences.
  - **Response Body:** `EssayHeatmap` with sentence-level feedback.

- **`GET /api/essays/:essayId/gap-analysis`**
  - **Description:** Fetches topic gap analysis for the essay.
  - **Response Body:** `TopicGapAnalysis` with missing syllabus areas and recommendations.

- **`POST /api/essays/paraphrase`**
  - **Description:** AI-powered sentence paraphrasing to formal CSS style.
  - **Request Body:** `{ sentence: string, style: string }`
  - **Response Body:** `{ alternatives: string[], confidence: number }`

- **`POST /api/essays/handwriting`**
  - **Description:** Upload handwritten essay for OCR processing and analysis.
  - **Request Body:** `FormData` with image file
  - **Response Body:** `{ text: string, clarityScore: number, improvementTips: string[] }`

- **`GET /api/essays/user/:userId`**
  - **Description:** Fetches all essays by a user.
  - **Response Body:** `Essay[]` with analysis summaries.

## 🔄 Peer Review API

- **`POST /api/essays/:essayId/submit-for-review`**
  - **Description:** Submits an essay for peer review.
  - **Request Body:** `{ anonymous: boolean }`
  - **Response Body:** `{ reviewId: string, estimatedWaitTime: string }`

- **`GET /api/reviews/available`**
  - **Description:** Fetches essays available for peer review.
  - **Response Body:** `Essay[]` (anonymized)

- **`POST /api/reviews/:reviewId/submit`**
  - **Description:** Submits a peer review for an essay.
  - **Request Body:** `{ rubricScores: object, feedback: string, quality: number }`
  - **Response Body:** `{ reviewId: string, qualityScore: number }`

- **`GET /api/essays/:essayId/reviews`**
  - **Description:** Fetches all peer reviews for an essay.
  - **Response Body:** `Review[]` with rubric scores and feedback.

- **`GET /api/reviews/user/:userId`**
  - **Description:** Fetches all reviews submitted by a user.
  - **Response Body:** `Review[]` with quality metrics.

## 🎯 Daily Challenges API

- **`GET /api/challenges/daily`**
  - **Description:** Fetches today's daily past paper challenge.
  - **Response Body:** `{ challengeId: string, question: string, timeLimit: number, subject: string }`

- **`POST /api/challenges/:challengeId/submit`**
  - **Description:** Submits answer for daily challenge.
  - **Request Body:** `{ answer: string, timeSpent: number }`
  - **Response Body:** `{ score: number, feedback: string, streak: number }`

- **`GET /api/challenges/streak`**
  - **Description:** Fetches user's challenge streak information.
  - **Response Body:** `{ currentStreak: number, longestStreak: number, nextMilestone: string }`

## 🏆 Gamification & Achievements API

- **`GET /api/badges`**
  - **Description:** Fetches all available achievement badges.
  - **Response Body:** `Badge[]` with criteria and progress.

- **`GET /api/badges/user/:userId`**
  - **Description:** Fetches user's earned badges and progress.
  - **Response Body:** `{ earned: Badge[], progress: BadgeProgress[] }`

- **`POST /api/badges/:badgeId/claim`**
  - **Description:** Claims an earned badge.
  - **Response Body:** `{ badgeId: string, claimedAt: string }`

- **`GET /api/leaderboards`**
  - **Description:** Fetches various leaderboards.
  - **Query Params:** `type: 'weekly' | 'monthly' | 'all-time'`
  - **Response Body:** `LeaderboardEntry[]` with user rankings.

## 📊 Advanced Analytics API

- **`GET /api/user/progress-radar`**
  - **Description:** Fetches multi-dimensional progress data for radar chart.
  - **Response Body:** `{ dimensions: ProgressDimension[], trends: TrendData[] }`

- **`GET /api/user/weak-topics`**
  - **Description:** Fetches weak topic analysis and recommendations.
  - **Response Body:** `{ weakTopics: WeakTopic[], recommendations: Recommendation[] }`

- **`POST /api/user/weak-topics/alert`**
  - **Description:** Configures weak topic alert preferences.
  - **Request Body:** `{ enabled: boolean, frequency: string, types: string[] }`
  - **Response Body:** `{ success: boolean }`

- **`GET /api/analytics/user/:userId`**
  - **Description:** Fetches comprehensive user analytics.
  - **Response Body:** `UserAnalytics` with performance metrics and trends.

## 🔔 Notifications API

- **`GET /api/notifications`**
  - **Description:** Fetches user's notifications.
  - **Response Body:** `Notification[]`

- **`POST /api/notifications/:notificationId/read`**
  - **Description:** Marks a notification as read.
  - **Response Body:** `{ success: boolean }`

- **`POST /api/notifications/preferences`**
  - **Description:** Updates notification preferences.
  - **Request Body:** `{ email: boolean, push: boolean, inApp: boolean, types: string[] }`
  - **Response Body:** `{ success: boolean }`

## 👤 User Profile & Progress API

- **`GET /api/user/me`**
  - **Description:** Fetches the profile and comprehensive progress for the currently authenticated user.
  - **Response Body:** `User` with `profile`, `testResults`, `essays`, `badges`, and `analytics` included.

- **`PUT /api/user/profile`**
  - **Description:** Updates the user's profile.
  - **Request Body:** `{ bio: string, name?: string, image?: string }`
  - **Response Body:** `Profile`

- **`GET /api/user/activity`**
  - **Description:** Fetches user's recent activity feed.
  - **Response Body:** `ActivityEntry[]` with timestamps and details.

## 💬 Forum API (Enhanced)

- **`GET /api/posts`**
  - **Description:** Fetches all forum posts with AI moderation status.
  - **Response Body:** `Post[]` with `author` and `moderationStatus` included.

- **`GET /api/posts/:postId`**
  - **Description:** Fetches a single post with its comments and AI moderation.
  - **Response Body:** `Post` with `comments`, `author`, and `moderationDetails` included.

- **`POST /api/posts`**
  - **Description:** Creates a new forum post with AI content analysis.
  - **Request Body:** `{ title: string, content: string }`
  - **Response Body:** `Post` with moderation status.

- **`POST /api/posts/:postId/comments`**
  - **Description:** Adds a comment to a post with AI moderation.
  - **Request Body:** `{ content: string }`
  - **Response Body:** `Comment` with moderation status.

## 👑 Admin API (Comprehensive)

All routes under `/api/admin/*` will be protected and only accessible by users with the `ADMIN` role.

### Content Management
- **`GET /api/admin/subjects`**: List all subjects.
- **`POST /api/admin/subjects`**: Create a new subject.
- **`PUT /api/admin/subjects/:subjectId`**: Update a subject.
- **`DELETE /api/admin/subjects/:subjectId`**: Delete a subject.

- **`GET /api/admin/essays`**: List all essays with moderation status.
- **`PUT /api/admin/essays/:essayId/moderate`**: Moderate essay content.
- **`DELETE /api/admin/essays/:essayId`**: Delete inappropriate essay.

- **`GET /api/admin/reviews`**: List all peer reviews with quality metrics.
- **`PUT /api/admin/reviews/:reviewId/moderate`**: Moderate review content.

### User Management
- **`GET /api/admin/users`**: List all users with roles and activity.
- **`PUT /api/admin/users/:userId/role`**: Update user role.
- **`DELETE /api/admin/users/:userId`**: Deactivate user account.

### Analytics & Monitoring
- **`GET /api/admin/analytics/overview`**: System-wide analytics dashboard.
- **`GET /api/admin/analytics/ai-performance`**: AI feature performance metrics.
- **`GET /api/admin/analytics/user-engagement`**: User engagement and retention data.

### Badge Management
- **`GET /api/admin/badges`**: List all badges with criteria.
- **`POST /api/admin/badges`**: Create new badge.
- **`PUT /api/admin/badges/:badgeId`**: Update badge criteria.
- **`DELETE /api/admin/badges/:badgeId`**: Delete badge.

### Content Moderation
- **`GET /api/admin/moderation/queue`**: Content pending moderation.
- **`POST /api/admin/moderation/:contentId/approve`**: Approve flagged content.
- **`POST /api/admin/moderation/:contentId/reject`**: Reject flagged content.

## 🤖 AI Processing API

- **`POST /api/ai/analyze-essay`**
  - **Description:** Initiates AI essay analysis (async processing).
  - **Request Body:** `{ essayId: string, analysisType: string[] }`
  - **Response Body:** `{ taskId: string, estimatedCompletion: string }`

- **`GET /api/ai/tasks/:taskId`**
  - **Description:** Checks status of AI processing task.
  - **Response Body:** `{ status: string, progress: number, result?: any }`

- **`POST /api/ai/ocr`**
  - **Description:** OCR processing for handwritten content.
  - **Request Body:** `FormData` with image
  - **Response Body:** `{ text: string, confidence: number, suggestions: string[] }`

## 📱 Mobile App API

- **`GET /api/mobile/offline-content`**
  - **Description:** Fetches content available for offline download.
  - **Response Body:** `{ materials: Material[], videos: Video[] }`

- **`POST /api/mobile/sync`**
  - **Description:** Syncs offline data when connection restored.
  - **Request Body:** `{ offlineData: any }`
  - **Response Body:** `{ success: boolean, conflicts: any[] }`

## 🔧 System Health API

- **`GET /api/health`**
  - **Description:** Basic health check.
  - **Response Body:** `{ status: string, timestamp: string }`

- **`GET /api/health/ai`**
  - **Description:** AI service health check.
  - **Response Body:** `{ status: string, services: AIServiceStatus[] }`

- **`GET /api/health/db`**
  - **Description:** Database health check.
  - **Response Body:** `{ status: string, connection: boolean }`

## 📋 API Conventions

### Status Codes
- `200` - Success
- `201` - Created
- `202` - Accepted (for async operations)
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Validation Error
- `429` - Rate Limited
- `500` - Internal Server Error

### Error Response Format
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "content",
      "issue": "Content cannot be empty"
    }
  }
}
```

### Pagination
- **Query Parameters:** `?page=1&limit=20&cursor=abc123`
- **Response Format:**
```json
{
  "items": [],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "hasNext": true,
    "nextCursor": "def456"
  }
}
```

### AI Processing
- **Async Operations:** Return `202 Accepted` with task ID
- **Progress Tracking:** Poll `/api/ai/tasks/:taskId` for status
- **Rate Limiting:** Implement per-user rate limits for AI features 