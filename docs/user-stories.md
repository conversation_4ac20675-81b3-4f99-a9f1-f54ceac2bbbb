# CSS Dost — User Stories and Acceptance Criteria

This document defines detailed user stories for the CSS Dost platform and near-term roadmap, including acceptance criteria and edge cases. It is intended to guide both the web app and the future mobile app (React Native) that will reuse the same backend APIs.

Roles used below come from the current schema and plan:
- Guest (Unauthenticated)
- User (Authenticated Student)
- <PERSON><PERSON> (Administrator)

Assumptions:
- Auth is provided by Supabase (email/password + OAuth). Sessions are handled via Supabase cookies and Next.js middleware.
- Backend uses REST under `/api` and Prisma models in `schema.prisma`.

## 1) Authentication & Account

### 1.1 As a Guest, I can register with email/password
- Acceptance criteria
  - Given I am on the registration screen, when I submit a valid email and password, then my account is created and I am signed in.
  - Password policy: at least 8 chars; server validates and returns a clear error if invalid.
  - If email already exists, I get a 409 conflict with a friendly message.
- Edge cases
  - Network interruption during submit → clear error; form remains populated.
  - Duplicate rapid submits → server safely prevents duplicate user creation (idempotent-by-unique-email).

### 1.2 As a Guest, I can sign in with email/password
- Acceptance criteria
  - Valid credentials → 200 OK, authenticated session established; redirected to dashboard.
  - Invalid credentials → 401 with generic message (do not reveal whether email exists).
- Edge cases
  - Rate limiting after repeated failures.

### 1.3 As a Guest, I can sign in with Google
- Acceptance criteria
  - Clicking Google starts OAuth; on success, account is created (if new) and I am signed in.
  - If email is already in use by credentials auth, accounts are linked or a clear resolution path is presented (config-dependent; MVP can disallow linking and return a message to sign in with original method).

### 1.4 As a User, I can sign out
- Acceptance criteria
  - Clicking sign out clears session server-side and client cookies; I am redirected to the home page.

### 1.5 As a User, I can view and edit my profile (bio, name, image)
- Acceptance criteria
  - GET `/api/user/me` returns current user with profile and comprehensive progress summary.
  - PUT `/api/user/profile` updates `bio` (and optionally `name`, `image`), returns updated profile.
  - Only the owner can update their profile; Admin cannot change user profile via this endpoint.
- Edge cases
  - Large/bad payloads → 400 validation error.

### 1.6 (Future) I can request a password reset link
- Acceptance criteria
  - POST `/api/auth/password/reset` accepts email; sends email if account exists; always 200 to avoid user enumeration.

## 2) Subjects & Content Browsing

### 2.1 As a Guest, I can browse a list of subjects
- Acceptance criteria
  - GET `/api/subjects` returns paginated list with `{ id, title, description }`.
  - Web shows a grid of `SubjectCard`s.
- Edge cases
  - Empty database shows an empty state UI.

### 2.2 As a Guest, I can view a subject's details
- Acceptance criteria
  - GET `/api/subjects/:subjectId` returns subject with `materials[]`, `videos[]`, and `mindMaps[]`.
  - Materials contain `title`, `contentUrl`, `contentType`.
  - Videos contain `title`, `youtubeUrl`.
  - Mind maps contain `title`, `visualizationUrl`, `coveragePercentage`.
- Edge cases
  - Invalid `subjectId` → 404.

### 2.3 As a Guest, I can open a study material
- Acceptance criteria
  - Clicking a material opens the external `contentUrl` in a new tab (web) or an in-app browser (mobile).
  - No file hosting in MVP; links are external.

### 2.4 As a Guest, I can view videos for a subject
- Acceptance criteria
  - YouTube URLs are embedded on web; deep-link capable on mobile.
  - Optional server route to fetch list via YouTube API if dynamic.

### 2.5 As a Guest/User, I can search/filter subjects and materials
- Acceptance criteria
  - Query params supported: `q`, `page`, `limit`, `sort`.
  - Search matches title and description (basic LIKE for MVP).

### 2.6 As a User, I can view AI-generated syllabus mind maps
- Acceptance criteria
  - GET `/api/subjects/:subjectId/mind-map` returns visual breakdown of syllabus coverage.
  - Mind map updates based on user's progress and test results.
  - Interactive elements show topic mastery levels.

## 3) Mock Tests & Quizzes

### 3.1 As a User, I can list tests for a subject
- Acceptance criteria
  - GET `/api/subjects/:subjectId` includes `tests[]` or a separate `/api/tests?subjectId=...`.
  - Each test: `{ id, title, subjectId }`.

### 3.2 As a User, I can start a test
- Acceptance criteria
  - POST `/api/tests/start` with `{ testId }` returns `{ testSessionId, questions }` (questions exclude `isCorrect`).
  - Server sets a `startTime` and a `duration` (if timed) on the session.
- Edge cases
  - Starting multiple active sessions for same test returns the current active one for MVP or allows multiple (defined behavior must be documented; MVP: allow multiple, store all results).

### 3.3 As a User, I can answer questions and submit the test
- Acceptance criteria
  - POST `/api/tests/submit` with `{ testSessionId, answers: [{ questionId, optionId }] }` calculates score and persists `TestResult`.
  - Response contains `{ testResultId, score }`.
  - Score equals the count of correct answers / total questions.
- Edge cases
  - Missing or malformed answers → 400 with validation details.
  - Submitting twice → 409 or returns the prior result (idempotent submission per session).
  - Expired timer → 400 with `reason: "expired"`.

### 3.4 As a User, I can view my past test results
- Acceptance criteria
  - GET `/api/tests/results/:resultId` returns the result if I am the owner; Admin can access any.
  - Dashboard lists results with `{ testTitle, score, createdAt }`.

### 3.5 As a User, I can retake a test
- Acceptance criteria
  - Starting the same test later creates a new session and result; historical results are preserved.

### 3.6 As a User, my progress is tracked
- Acceptance criteria
  - `TestResult` stored with `userId`, `testId`, `score`, `createdAt`.
  - Dashboard aggregates recent scores and provides data for comprehensive analytics.

### 3.7 Robustness during testing
- Edge cases
  - Refresh mid-test → client can refetch current session by `testSessionId` (optional MVP). If not implemented, refresh discards client state and user may restart.
  - Network loss while answering → answers buffered locally until submit; server validates on submit only.

## 4) Essay Writing & AI Analysis

### 4.1 As a User, I can write and submit essays
- Acceptance criteria
  - POST `/api/essays` with `{ title, content, subjectId }` creates an essay attributed to me.
  - Essay is automatically analyzed for grammar, vocabulary, and relevance.
  - Response includes `{ essayId, analysisResults }`.
- Edge cases
  - Empty or too long content → 400 with validation details.
  - AI analysis failure → graceful degradation with basic validation only.

### 4.2 As a User, I can view essay quality heatmap
- Acceptance criteria
  - GET `/api/essays/:essayId/heatmap` returns color-coded feedback per sentence.
  - Colors: Green (excellent), Yellow (good), Orange (needs improvement), Red (poor).
  - Each sentence includes detailed feedback tooltips.
- Edge cases
  - Analysis in progress → show loading state with estimated completion time.

### 4.3 As a User, I can get topic gap analysis
- Acceptance criteria
  - GET `/api/essays/:essayId/gap-analysis` returns missing syllabus areas.
  - Analysis includes specific study recommendations and resource suggestions.
  - Results are stored for progress tracking.
- Edge cases
  - No gaps detected → show positive reinforcement message.

### 4.4 As a User, I can use exam-style paraphrasing coach
- Acceptance criteria
  - POST `/api/essays/paraphrase` with `{ sentence, style }` returns formal CSS exam style alternatives.
  - Multiple style options provided for each sentence.
  - User can accept or reject suggestions.
- Edge cases
  - Complex sentences → provide multiple paraphrasing options.
  - Technical terms → preserve accuracy while improving formality.

### 4.5 As a User, I can upload handwritten essays for analysis
- Acceptance criteria
  - POST `/api/essays/handwriting` with image file triggers OCR processing.
  - OCR converts handwriting to text for analysis.
  - Handwriting clarity score and improvement tips provided.
- Edge cases
  - Poor image quality → clear error message with improvement suggestions.
  - OCR failure → manual text input option.

## 5) Daily Engagement Features

### 5.1 As a User, I can participate in daily past paper challenges
- Acceptance criteria
  - GET `/api/challenges/daily` returns today's challenge question.
  - Challenge includes timer (15-30 minutes) and AI scoring.
  - Results contribute to streak tracking and leaderboards.
- Edge cases
  - Multiple attempts → track best score for the day.
  - Timer expiration → auto-submit with current answers.

### 5.2 As a User, I can track my study streaks
- Acceptance criteria
  - Dashboard shows current streak and longest streak.
  - Streak increases with daily activity (tests, essays, challenges).
  - Streak breaks after missing consecutive days.
- Edge cases
  - Timezone differences → use user's local timezone for streak calculation.

## 6) Peer Review System

### 6.1 As a User, I can submit essays for peer review
- Acceptance criteria
  - POST `/api/essays/:essayId/submit-for-review` makes essay available for peer review.
  - Essay is anonymized and matched with appropriate reviewers.
  - Review assignment based on subject expertise and availability.
- Edge cases
  - No available reviewers → queue essay for later review.

### 6.2 As a User, I can review other users' essays
- Acceptance criteria
  - GET `/api/reviews/available` returns essays available for review.
  - Guided rubric system ensures consistent feedback.
  - Reviews are anonymous and moderated by AI.
- Edge cases
  - Conflict of interest → avoid matching with known users.
  - Review quality issues → AI flags for manual review.

### 6.3 As a User, I can view feedback on my essays
- Acceptance criteria
  - GET `/api/essays/:essayId/reviews` returns all peer reviews.
  - Reviews include rubric scores and detailed feedback.
  - AI moderation ensures review quality and appropriateness.
- Edge cases
  - Inappropriate reviews → automatically flagged and hidden.

## 7) Gamification & Achievements

### 7.1 As a User, I can earn achievement badges
- Acceptance criteria
  - Badges automatically unlocked based on performance metrics.
  - Badge categories: Grammar, Vocabulary, Essay Count, Streaks, Peer Reviews.
  - Badge progress tracking shows progress toward next achievement.
- Edge cases
  - Edge case achievements → special badges for unique accomplishments.

### 7.2 As a User, I can view leaderboards
- Acceptance criteria
  - GET `/api/leaderboards` returns various leaderboards (weekly, monthly, all-time).
  - Leaderboards include scores, streaks, and achievement counts.
  - User's position highlighted in results.
- Edge cases
  - Tie scores → alphabetical ordering or shared positions.

## 8) Advanced Analytics

### 8.1 As a User, I can view my progress radar chart
- Acceptance criteria
  - GET `/api/user/progress-radar` returns multi-dimensional progress data.
  - Dimensions: English, syllabus coverage, writing style, and speed.
  - Chart shows trends over time and peer comparisons.
- Edge cases
  - Insufficient data → show encouraging message with data collection progress.

### 8.2 As a User, I can receive weak topic alerts
- Acceptance criteria
  - System automatically detects consistently low-scoring areas.
  - Push notifications or in-app alerts for weak topics.
  - Alerts include actionable improvement suggestions.
- Edge cases
  - Notification preferences → user can customize alert frequency and types.

## 9) Forum (Enhanced)

### 9.1 As a Guest, I can view posts and comments
- Acceptance criteria
  - GET `/api/posts` returns paginated posts with author summary.
  - GET `/api/posts/:postId` returns post with comments.
- Edge cases
  - 404 for unknown post.

### 9.2 As a User, I can create posts and comment
- Acceptance criteria
  - POST `/api/posts` with `{ title, content }` creates a post attributed to me.
  - POST `/api/posts/:postId/comments` with `{ content }` creates a comment.
  - AI moderation ensures content quality and appropriateness.
- Edge cases
  - Empty or too long content → 400 with validation details.
  - Inappropriate content → AI flags for manual review.

### 9.3 As a User, I can edit/delete my own posts/comments
- Acceptance criteria
  - PUT/DELETE endpoints only allowed for resource owner or Admin.
  - Edit history tracked for moderation purposes.

### 9.4 As an Admin, I can moderate posts/comments
- Acceptance criteria
  - Admin can delete any post/comment.
  - AI-assisted moderation tools for content filtering.
  - Manual review queue for flagged content.

## 10) Admin Panel & Content Management

### 10.1 As an Admin, I can manage subjects
- Acceptance criteria
  - CRUD endpoints under `/api/admin/subjects`.
  - Validation: title required and unique; description optional.

### 10.2 As an Admin, I can manage materials and videos
- Acceptance criteria
  - CRUD under `/api/admin/materials` and `/api/admin/videos` (or nested under subject routes).
  - Validation: `contentUrl` must be a valid URL; `youtubeUrl` must be a valid YouTube URL.

### 10.3 As an Admin, I can manage tests, questions, and options
- Acceptance criteria
  - CRUD under `/api/admin/tests`, `/api/admin/questions`, `/api/admin/options`.
  - One `Option.isCorrect` per question enforced server-side.

### 10.4 As an Admin, I can manage essays and reviews
- Acceptance criteria
  - CRUD under `/api/admin/essays` and `/api/admin/reviews`.
  - Content moderation tools for inappropriate content.
  - Review quality assessment and user reputation management.

### 10.5 As an Admin, I can view comprehensive analytics dashboard
- Acceptance criteria
  - Summary counts: subjects, materials, tests, users, posts, essays, reviews.
  - User engagement metrics and retention analysis.
  - AI feature performance and accuracy metrics.
  - System health monitoring and error reporting.

### 10.6 As an Admin, I can manage achievement badges
- Acceptance criteria
  - CRUD under `/api/admin/badges`.
  - Badge criteria configuration and automation rules.
  - Badge assignment and revocation tools.

## 11) Mobile App (React Native) Considerations

- The backend API will be shared by web and mobile. Requirements:
  - Stable REST endpoints with explicit versioning for public clients (consider `/api/v1/*`).
  - JSON responses use a consistent envelope: `{ data, error }` (MVP can keep raw objects but define a consistent error shape).
  - Pagination: cursor-based when lists may grow large (`cursor`, `limit`).
  - Auth: mobile clients use secure token storage; server validates JWTs; CORS enabled for mobile dev origins if needed.
  - Idempotency: creation endpoints should be robust to retries (clients may retry on flaky networks).
  - Rate limiting and abuse protection for public endpoints (especially forum and auth routes).
  - Error messages localized client-side; server returns codes + keys where possible.
  - **Camera Integration:** Direct handwriting upload and analysis.
  - **Push Notifications:** Daily challenges, weak topic alerts, and achievement notifications.
  - **Offline Mode:** Download study materials for offline access.

## 12) Non-Functional Requirements

- Security
  - Server-side validation on all inputs; sanitize strings; enforce auth and role checks on protected routes.
  - Protect secrets and never expose service keys to the client.
  - AI data privacy: user data anonymized for training where applicable.
- Performance & Reliability
  - Paginate list endpoints; include basic indexes where needed (e.g., on foreign keys).
  - Implement sensible timeouts and avoid N+1 queries.
  - AI processing: async processing with progress tracking for long-running operations.
- Observability
  - Log request errors with correlation ids; redact PII in logs.
  - AI feature monitoring: track accuracy, response times, and user satisfaction.
- Accessibility (web)
  - Keyboard navigation, ARIA labels for interactive components.
  - Color contrast compliance for heatmap visualizations.

## 13) API Conventions (Enhanced)

- Status codes
  - 200 for success, 201 for creation, 400 for validation errors, 401 unauthenticated, 403 unauthorized, 404 not found, 409 conflict, 500 server error.
- Error format
  - `{ error: { code: string, message: string, details?: any } }`.
- Pagination
  - Query: `?cursor=...&limit=...`; Response: `{ items: T[], nextCursor?: string }`.
- Sorting/Filtering
  - `?sort=createdAt:desc&filter[field]=value` (MVP: limited filters as needed).
- AI Processing
  - Async operations: `202 Accepted` with `{ taskId: string, estimatedCompletion: string }`.
  - Progress tracking: `GET /api/tasks/:taskId` returns `{ status, progress, result }`.

## 14) Data Privacy & Retention (Enhanced)

- Users can request profile deletion (future); Admin can remove content violating guidelines.
- Minimal PII stored: name, email, optional image and bio.
- AI data handling: user consent for data usage in AI training.
- Content retention: essays and reviews retained for learning purposes unless deleted by user.

## 15) Out-of-Scope (Current Phase)

- Advanced moderation (reports, flags, shadow bans) - basic AI moderation only.
- Rich text uploads/attachments for forum posts - plain text only.
- Payment/subscription - free platform for current phase.
- Full-blown analytics - basic progress tracking and insights only.
- Real-time collaboration - async peer review only.
- Advanced AI features - basic analysis and paraphrasing only.

---

This document should be kept in sync with `features-breakdown.md`, `api-structure.md`, and `schema.prisma` as the project evolves. For each implemented endpoint, add concrete request/response examples and expand acceptance criteria with exact validation rules.
